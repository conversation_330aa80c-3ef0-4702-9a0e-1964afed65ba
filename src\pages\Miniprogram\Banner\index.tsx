import { create, index, remove, update, updatePriority } from '@/services/banner';
import { ActionType, ProColumns, ProTable } from '@ant-design/pro-components';
import { Button, Image, message, Popconfirm, Space, Tag, InputNumber } from 'antd';
import React, { useRef, useState } from 'react';
import EditModal from './EditModal';

const BannerManagement: React.FC = () => {
  const actionRef = useRef<ActionType>();
  const [modalVisible, setModalVisible] = useState(false);
  const [current, setCurrent] = useState<API.Banner | undefined>(undefined);

  const handleSave = async (values: API.Banner) => {
    let response;
    if (current) {
      const { id, ...info } = values;
      response = await update(id, info);
    } else {
      response = await create(values);
    }

    if (response.errCode) {
      message.error(response.msg);
    } else {
      message.success('操作成功');
      actionRef?.current?.reload();
      setModalVisible(false);
    }
  };

  const handleDel = async (record: API.Banner) => {
    const { id } = record;
    const response = await remove(id);
    if (response.errCode) {
      message.error(response.msg);
    } else {
      message.success('删除成功');
      actionRef?.current?.reload();
    }
  };

  const handlePriorityChange = async (id: number, priority: number) => {
    const response = await updatePriority(id, priority);
    if (response.errCode) {
      message.error(response.msg);
    } else {
      message.success('优先级更新成功');
      actionRef?.current?.reload();
    }
  };

  const columns: ProColumns<API.Banner, 'text'>[] = [
    {
      title: 'ID',
      dataIndex: 'id',
      key: 'id',
      hidden: true,
      hideInSearch: true,
    },
    {
      title: '图片',
      dataIndex: 'imageURL',
      key: 'imageURL',
      hideInSearch: true,
      render: (_, record) => {
        return <Image src={record.imageURL} alt="" width={120} height={80} style={{ objectFit: 'cover' }} />;
      },
    },
    {
      title: '跳转类型',
      dataIndex: 'jumpType',
      key: 'jumpType',
      valueType: 'select',
      valueEnum: {
        custom: { text: '自定义链接', status: 'Processing' },
        activity: { text: '活动页', status: 'Success' },
        null: { text: '无跳转', status: 'Default' },
      },
      render: (_, record) => {
        const typeMap = {
          custom: <Tag color="blue">自定义链接</Tag>,
          activity: <Tag color="green">活动页</Tag>,
          null: <Tag color="default">无跳转</Tag>,
        };
        return typeMap[record.jumpType as keyof typeof typeMap] || <Tag color="default">无跳转</Tag>;
      },
    },
    {
      title: '跳转链接',
      dataIndex: 'jumpLink',
      key: 'jumpLink',
      hideInSearch: true,
      ellipsis: true,
      render: (text) => text || '-',
    },
    {
      title: '优先级',
      dataIndex: 'priority',
      key: 'priority',
      width: 120,
      render: (_, record) => (
        <InputNumber
          min={1}
          max={10}
          value={record.priority}
          onChange={(value) => {
            if (value && value !== record.priority) {
              handlePriorityChange(record.id, value);
            }
          }}
          style={{ width: 80 }}
        />
      ),
    },
    {
      title: '创建时间',
      dataIndex: 'createdAt',
      key: 'createdAt',
      valueType: 'dateTime',
      hideInSearch: true,
      sorter: true,
    },
    {
      title: '操作',
      key: 'action',
      valueType: 'option',
      align: 'center',
      render: (_, record) => (
        <Space>
          <Button
            type="link"
            onClick={() => {
              setCurrent(record);
              setModalVisible(true);
            }}
          >
            编辑
          </Button>
          <Popconfirm
            title="确认删除？"
            onConfirm={() => {
              handleDel(record);
            }}
          >
            <Button type="link" danger>
              删除
            </Button>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  return (
    <>
      <ProTable<API.Banner>
        actionRef={actionRef}
        rowKey="id"
        columns={columns}
        request={async (params) => {
          const { errCode, msg, data } = await index(params);
          if (errCode) {
            message.error(msg || '列表查询失败');
            return {
              data: [],
              total: 0,
            };
          }
          return {
            data: data?.list || [],
            total: data?.total || 0,
          };
        }}
        toolBarRender={() => [
          <Button
            key="add"
            type="primary"
            onClick={() => {
              setCurrent(undefined);
              setModalVisible(true);
            }}
          >
            新增
          </Button>,
        ]}
      />
      <EditModal
        open={modalVisible}
        info={current}
        onClose={() => setModalVisible(false)}
        onSave={handleSave}
      />
    </>
  );
};

export default BannerManagement;
