import ProFormImg from '@/components/ProFormItem/ProFormImg';
import WechatDomainWarning from '@/components/WechatDomainWarning';
import {
  ModalForm,
  ProFormDependency,
  ProFormRadio,
  ProFormText,
} from '@ant-design/pro-components';
import { Form } from 'antd';
import React, { useEffect } from 'react';
import styles from './EditModal.less';

type EditModalProps = {
  open: boolean;
  info?: API.Banner;
  onSave: (info: API.Banner) => Promise<void>;
  onClose: () => void;
};

const EditModal: React.FC<EditModalProps> = ({
  open,
  info,
  onSave,
  onClose,
}) => {
  const [form] = Form.useForm();

  // 当模态框打开时重置表单
  useEffect(() => {
    if (open) {
      if (info) {
        // 编辑模式：设置表单值
        form.setFieldsValue({
          ...info,
          jumpType: info.jumpType || null, // 确保 null 值正确设置
          priority: info.priority || 5, // 设置默认优先级
        });
      } else {
        // 新增模式：重置表单并设置默认值
        form.resetFields();
        form.setFieldsValue({
          jumpType: null,
          priority: 5,
        });
      }
    }
  }, [open, info, form]);

  return (
    <ModalForm<API.Banner>
      title={info ? '编辑图片' : '添加图片'}
      autoFocusFirstInput
      modalProps={{
        destroyOnClose: true,
        onCancel: onClose,
      }}
      open={open}
      layout="horizontal"
      grid
      labelCol={{ flex: '7em' }}
      onFinish={onSave}
      form={form}
    >
      <ProFormText name="id" label="ID" hidden />
      <div className={styles.bannerUpload}>
        <ProFormImg
          name="imageURL"
          label="图片"
          dir="banner"
          maxSize={{ size: 1024 * 1024 * 2, message: '图片大小不能超过2M' }}
          rules={[{ required: true, message: '请选择图片！' }]}
          extra="建议尺寸比例为 2:1 的横向长方形图片，如：300×150px"
        />
      </div>
      <div className={styles.jumpTypeRadio}>
        <ProFormRadio.Group
          name="jumpType"
          label="跳转类型"
          options={[
            { label: '无跳转', value: null },
            { label: '自定义链接', value: 'custom' },
            { label: '活动页', value: 'activity' },
          ]}
          initialValue={null}
        />
      </div>
      <div className={styles.priorityRadio}>
        <ProFormRadio.Group
          name="priority"
          label="优先级"
          tooltip="数值越大优先级越高，默认为5"
          options={[
            { label: '1', value: 1 },
            { label: '2', value: 2 },
            { label: '3', value: 3 },
            { label: '4', value: 4 },
            { label: '5', value: 5 },
            { label: '6', value: 6 },
            { label: '7', value: 7 },
            { label: '8', value: 8 },
            { label: '9', value: 9 },
            { label: '10', value: 10 },
          ]}
          initialValue={5}
          rules={[{ required: true, message: '请选择优先级！' }]}
        />
      </div>
      <ProFormDependency name={['jumpType']}>
        {({ jumpType }) => {
          if (jumpType === 'custom') {
            return (
              <ProFormText
                name="jumpLink"
                label="跳转链接"
                placeholder="请输入跳转链接，如：https://example.com"
                rules={[
                  { required: true, message: '请输入跳转链接！' },
                  { type: 'url', message: '请输入有效的URL地址！' },
                ]}
                tooltip={{
                  title: (
                    <div>
                      <div
                        style={{
                          color: '#ff4d4f',
                          fontWeight: 'bold',
                          marginBottom: 8,
                        }}
                      >
                        ⚠️ 重要提示：微信小程序域名限制
                      </div>
                      <div style={{ marginBottom: 4 }}>
                        • 链接域名必须在微信小程序后台配置的
                        <strong>业务域名白名单</strong>中
                      </div>
                      <div style={{ marginBottom: 4 }}>
                        • 配置路径：微信公众平台 → 开发管理 → 开发设置 →
                        业务域名
                      </div>
                      <div style={{ marginBottom: 4 }}>
                        • 未配置的域名将无法在小程序中正常跳转
                      </div>
                      <div style={{ color: '#1890ff' }}>
                        • 建议使用已备案的自有域名或已配置的域名
                      </div>
                    </div>
                  ),
                  overlayStyle: { maxWidth: 400 },
                }}
              />
            );
          }
          return null;
        }}
      </ProFormDependency>

      {/* 域名限制警告 - 只在选择自定义链接时显示 */}
      <ProFormDependency name={['jumpType']}>
        {({ jumpType }) => {
          if (jumpType === 'custom') {
            return (
              <div style={{ width: '100%', marginBottom: 16 }}>
                <WechatDomainWarning compact />
              </div>
            );
          }
          return null;
        }}
      </ProFormDependency>
    </ModalForm>
  );
};

export default EditModal;
