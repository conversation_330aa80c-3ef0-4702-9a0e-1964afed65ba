import ProFormImg from '@/components/ProFormItem/ProFormImg';
import WechatDomainWarning from '@/components/WechatDomainWarning';
import { ModalForm, ProFormText } from '@ant-design/pro-components';
import { Form } from 'antd';
import React, { useEffect } from 'react';

type EditModalProps = {
  open: boolean;
  info?: API.Banner;
  onSave: (info: API.Banner) => Promise<void>;
  onClose: () => void;
};

const EditModal: React.FC<EditModalProps> = ({
  open,
  info,
  onSave,
  onClose,
}) => {
  const [form] = Form.useForm();

  // 当模态框打开时重置表单
  useEffect(() => {
    if (open) {
      if (info) {
        // 编辑模式：设置表单值
        form.setFieldsValue(info);
      } else {
        // 新增模式：重置表单
        form.resetFields();
      }
    }
  }, [open, info, form]);

  return (
    <ModalForm<API.Banner>
      title={info ? '编辑图片' : '添加图片'}
      autoFocusFirstInput
      modalProps={{
        destroyOnClose: true,
        onCancel: onClose,
      }}
      open={open}
      layout="horizontal"
      grid
      labelCol={{ flex: '7em' }}
      onFinish={onSave}
      form={form}
    >
      <ProFormText name="id" label="ID" hidden />
      <ProFormImg
        name="imageURL"
        label="图片"
        dir="banner"
        maxSize={{ size: 1024 * 1024 * 2, message: '图片大小不能超过2M' }}
        rules={[{ required: true, message: '请输入名称！' }]}
      />
      <ProFormText
        name="jumpLink"
        label="跳转链接"
        colProps={{ span: 18 }}
        placeholder="请输入跳转链接，如：https://example.com"
        tooltip={{
          title: (
            <div>
              <div
                style={{
                  color: '#ff4d4f',
                  fontWeight: 'bold',
                  marginBottom: 8,
                }}
              >
                ⚠️ 重要提示：微信小程序域名限制
              </div>
              <div style={{ marginBottom: 4 }}>
                • 链接域名必须在微信小程序后台配置的
                <strong>业务域名白名单</strong>中
              </div>
              <div style={{ marginBottom: 4 }}>
                • 配置路径：微信公众平台 → 开发管理 → 开发设置 → 业务域名
              </div>
              <div style={{ marginBottom: 4 }}>
                • 未配置的域名将无法在小程序中正常跳转
              </div>
              <div style={{ color: '#1890ff' }}>
                • 建议使用已备案的自有域名或已配置的域名
              </div>
            </div>
          ),
          overlayStyle: { maxWidth: 400 },
        }}
      />
      <ProFormText name="sort" label="排序" colProps={{ span: 6 }} />

      {/* 域名限制警告 */}
      <div style={{ width: '100%', marginBottom: 16 }}>
        <WechatDomainWarning compact />
      </div>
    </ModalForm>
  );
};

export default EditModal;
