// 轮播图编辑模态框样式

// 图片上传组件样式 - 2:1 比例
.bannerUpload {
  :global(.ant-upload.ant-upload-select-picture-card) {
    width: 200px !important;
    height: 100px !important;
    border-radius: 6px;
  }

  :global(.ant-upload-list-picture-card .ant-upload-list-item) {
    width: 200px !important;
    height: 100px !important;
    border-radius: 6px;
  }

  :global(.ant-upload-list-picture-card .ant-upload-list-item-thumbnail img) {
    width: 100% !important;
    height: 100% !important;
    object-fit: cover !important;
    border-radius: 6px;
  }

  :global(.ant-upload-list-picture-card .ant-upload-list-item-actions) {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    display: flex;
    gap: 8px;
    background: rgba(0, 0, 0, 0.5);
    border-radius: 4px;
    padding: 4px 8px;

    .anticon {
      color: #fff;
      font-size: 16px;
      cursor: pointer;
      transition: all 0.3s;

      &:hover {
        color: #1890ff;
        transform: scale(1.1);
      }
    }
  }

  // 修复图片容器的相对定位
  :global(.ant-upload-list-picture-card .ant-upload-list-item) {
    position: relative;
    overflow: hidden;
  }

  // 优化图片缩略图容器
  :global(.ant-upload-list-picture-card .ant-upload-list-item-thumbnail) {
    width: 100% !important;
    height: 100% !important;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  // 悬停时显示操作按钮
  :global(.ant-upload-list-picture-card .ant-upload-list-item:hover .ant-upload-list-item-actions) {
    opacity: 1;
  }

  :global(.ant-upload-list-picture-card .ant-upload-list-item-actions) {
    opacity: 0;
    transition: opacity 0.3s;
  }

  // 响应式设计 - 保持2:1比例
  @media (max-width: 768px) {
    :global(.ant-upload.ant-upload-select-picture-card) {
      width: 160px !important;
      height: 80px !important;
    }

    :global(.ant-upload-list-picture-card .ant-upload-list-item) {
      width: 160px !important;
      height: 80px !important;
    }
  }

  @media (max-width: 480px) {
    :global(.ant-upload.ant-upload-select-picture-card) {
      width: 140px !important;
      height: 70px !important;
    }

    :global(.ant-upload-list-picture-card .ant-upload-list-item) {
      width: 140px !important;
      height: 70px !important;
    }
  }
}

// 优先级单选按钮样式
.priorityRadio {
  :global(.ant-radio-group) {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
  }

  :global(.ant-radio-wrapper) {
    margin-right: 0;
    padding: 4px 12px;
    border: 1px solid #d9d9d9;
    border-radius: 4px;
    transition: all 0.3s;

    &:hover {
      border-color: #1890ff;
      background-color: #f0f8ff;
    }
  }

  :global(.ant-radio-wrapper-checked) {
    border-color: #1890ff;
    background-color: #e6f7ff;
  }
}

// 跳转类型单选按钮样式
.jumpTypeRadio {
  :global(.ant-radio-group) {
    display: flex;
    gap: 16px;
  }

  :global(.ant-radio-wrapper) {
    margin-right: 0;
    padding: 8px 16px;
    border: 1px solid #d9d9d9;
    border-radius: 6px;
    transition: all 0.3s;
    background-color: #fafafa;

    &:hover {
      border-color: #1890ff;
      background-color: #f0f8ff;
    }
  }

  :global(.ant-radio-wrapper-checked) {
    border-color: #1890ff;
    background-color: #e6f7ff;
    color: #1890ff;
    font-weight: 500;
  }
}
