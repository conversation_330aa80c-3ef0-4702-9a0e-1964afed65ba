// 轮播图编辑模态框样式

// 图片上传组件样式 - 2:1 比例
.bannerUpload {
  :global(.ant-upload.ant-upload-select-picture-card) {
    width: 200px !important;
    height: 100px !important;
    border-radius: 6px;
  }

  :global(.ant-upload-list-picture-card .ant-upload-list-item) {
    width: 200px !important;
    height: 100px !important;
    border-radius: 6px;
  }

  :global(.ant-upload-list-picture-card .ant-upload-list-item-thumbnail img) {
    width: 100% !important;
    height: 100% !important;
    object-fit: cover !important;
    border-radius: 6px;
  }

  // 响应式设计 - 保持2:1比例
  @media (max-width: 768px) {
    :global(.ant-upload.ant-upload-select-picture-card) {
      width: 160px !important;
      height: 80px !important;
    }

    :global(.ant-upload-list-picture-card .ant-upload-list-item) {
      width: 160px !important;
      height: 80px !important;
    }
  }

  @media (max-width: 480px) {
    :global(.ant-upload.ant-upload-select-picture-card) {
      width: 140px !important;
      height: 70px !important;
    }

    :global(.ant-upload-list-picture-card .ant-upload-list-item) {
      width: 140px !important;
      height: 70px !important;
    }
  }
}

// 优先级单选按钮样式
.priorityRadio {
  :global(.ant-radio-group) {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
  }

  :global(.ant-radio-wrapper) {
    margin-right: 0;
    padding: 4px 12px;
    border: 1px solid #d9d9d9;
    border-radius: 4px;
    transition: all 0.3s;

    &:hover {
      border-color: #1890ff;
      background-color: #f0f8ff;
    }
  }

  :global(.ant-radio-wrapper-checked) {
    border-color: #1890ff;
    background-color: #e6f7ff;
  }
}

// 跳转类型单选按钮样式
.jumpTypeRadio {
  :global(.ant-radio-group) {
    display: flex;
    gap: 16px;
  }

  :global(.ant-radio-wrapper) {
    margin-right: 0;
    padding: 8px 16px;
    border: 1px solid #d9d9d9;
    border-radius: 6px;
    transition: all 0.3s;
    background-color: #fafafa;

    &:hover {
      border-color: #1890ff;
      background-color: #f0f8ff;
    }
  }

  :global(.ant-radio-wrapper-checked) {
    border-color: #1890ff;
    background-color: #e6f7ff;
    color: #1890ff;
    font-weight: 500;
  }
}
